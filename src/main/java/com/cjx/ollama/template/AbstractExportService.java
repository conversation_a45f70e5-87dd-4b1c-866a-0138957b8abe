package com.cjx.ollama.template;

import com.cjx.ollama.pojo.entity.ChatMessage;
import com.cjx.ollama.pojo.entity.ChatSession;
import com.cjx.ollama.pojo.info.SessionInfo;
import com.cjx.ollama.service.ChatMessageService;
import com.cjx.ollama.service.ExportService;
import com.cjx.ollama.utils.session.SessionUtil;

import java.util.List;

/**
 * 会话导出服务的抽象类，负责定义导出流程的固定部分。
 * --------------------------------------------------模板方法设计模式--------------------------------------------------
 * 模板方法：processExport方法定义了导出流程的 "骨架"（先验证、再处理），固定了步骤顺序
 * 钩子方法：doExport作为 "钩子"，允许子类自定义流程中的特定步骤（如不同格式的内容生成）
 * 处理会话导出的通用方法，封装会话验证和文件内容生成逻辑
 */
public abstract class AbstractExportService {

    /**
     * 模板方法：定义导出流程的骨架
     * 固定步骤顺序：验证参数 -> 获取会话数据 -> 生成内容
     */
    public final <T> T processExport(Long userId, String sessionId,
                                     ExportService exportService,
                                     ChatMessageService chatMessageService) {
        // 1. 获取验证后的会话和消息（固定步骤）
        SessionInfo sessionInfo = SessionUtil.getValidSessionAndMessages(
                userId,
                sessionId,
                exportService,
                chatMessageService
        );

        // 2. 调用钩子方法生成内容（可变步骤，由子类实现）
        return doExport(sessionInfo.session(), sessionInfo.messages());
    }

    /**
     * 钩子方法：由子类实现具体的导出逻辑
     * @param session 会话信息
     * @param messages 消息列表
     * @return 导出结果
     */
    protected abstract <T> T doExport(ChatSession session, List<ChatMessage> messages);
}