package com.cjx.ollama.template.exporter;

import com.cjx.ollama.component.ImageLoader;
import com.cjx.ollama.exception.CustomerException;
import com.cjx.ollama.pojo.entity.ChatMessage;
import com.cjx.ollama.pojo.entity.ChatSession;
import com.cjx.ollama.result.ResultEnum;
import com.cjx.ollama.template.AbstractExportService;
import com.cjx.ollama.utils.session.export.EscapeUtil;
import com.cjx.ollama.utils.session.export.FileWriteUtil;
import com.cjx.ollama.utils.session.export.MarkdownUtils;
import com.cjx.ollama.utils.session.export.image.ImageUtil;
import com.cjx.ollama.utils.session.export.link.LinkUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.StringWriter;
import java.util.List;

/**
 * CSV导出器
 */
@Slf4j
public class CsvExporter extends AbstractExportService {
    private final LinkUtil linkUtil;
    private final ImageUtil imageUtil;
    private final ImageLoader imageLoader;
    private final String postCategory;
    private final String imageCategory;

    public CsvExporter(Long userId, String sessionId,LinkUtil linkUtil, ImageUtil imageUtil, ImageLoader imageLoader,
                       String postCategory, String imageCategory) {
        super(userId, sessionId);
        this.linkUtil = linkUtil;
        this.imageUtil = imageUtil;
        this.imageLoader = imageLoader;
        this.postCategory = postCategory;
        this.imageCategory = imageCategory;
    }

    @Override
    protected String doExport(ChatSession session, List<ChatMessage> messages) {
        try (StringWriter stringWriter = new StringWriter()) {
            stringWriter.write('\uFEFF');
            stringWriter.write("序号,会话标题,消息内容（HTML）,文章分类,图片链接,关联链接\n");

            for (int i = 0; i < messages.size(); i++) {
                ChatMessage msg = messages.get(i);
                String htmlContent = MarkdownUtils.toHtml(msg.getContent());
                String htmlWithLink = linkUtil.insertLinks(htmlContent);
                ImageUtil.ImageInjectionResult imageResult = imageUtil.insertImageLink(
                        htmlWithLink, imageLoader.getImagesByCategory(imageCategory));

                String line = String.format("%d,%s,%s,%s,%s,%s%n",
                        i + 1,
                        EscapeUtil.escapeCsv(session.getSessionName()),
                        EscapeUtil.escapeCsv(imageResult.htmlContent()),
                        EscapeUtil.escapeCsv(postCategory),
                        EscapeUtil.escapeCsv(imageResult.imageUrl()),
                        "");
                stringWriter.write(line);
            }

            String csvContent = stringWriter.toString();
            FileWriteUtil.writeSessionCsv(session, csvContent);

            log.info("CSV 导出成功，userId: {}, sessionId: {}, 消息数量: {}",
                    getUserId(), getSessionId(), messages.size());
            return csvContent;

        } catch (Exception e) {
            log.error("CSV生成失败，sessionId: {}", getSessionId(), e);
            throw new CustomerException(ResultEnum.FILE_WRITE_ERROR);
        }
    }
}