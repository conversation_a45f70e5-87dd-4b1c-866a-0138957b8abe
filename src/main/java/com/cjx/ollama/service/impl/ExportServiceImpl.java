package com.cjx.ollama.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cjx.ollama.exception.CustomerException;
import com.cjx.ollama.mapper.ChatSessionMapper;
import com.cjx.ollama.pojo.entity.ChatSession;
import com.cjx.ollama.result.ResultEnum;
import com.cjx.ollama.service.ChatMessageService;
import com.cjx.ollama.service.ExportService;
import com.cjx.ollama.template.exporter.*;
import com.cjx.ollama.component.ImageLoader;
import com.cjx.ollama.utils.session.export.CategoryUtil;
import com.cjx.ollama.utils.session.export.FileWriteUtil;
import com.cjx.ollama.utils.session.export.image.ImageUtil;
import com.cjx.ollama.utils.session.export.link.LinkUtil;
import com.cjx.ollama.utils.verify.ValidationUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;
import static com.cjx.ollama.utils.constant.Export.DEFAULT_IMAGE_CATEGORY;
import static com.cjx.ollama.utils.constant.Export.DEFAULT_POST_CATEGORY;

/**
 * 会话导出服务实现类
 * 支持TXT/HTML/XML/Excel/CSV多种格式导出，区分文章分类和图片分类
 * 重构后使用模板方法模式，将通用导出流程提取到抽象类中
 * author cjx
 * date 2025/7/18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExportServiceImpl extends ServiceImpl<ChatSessionMapper, ChatSession> implements ExportService {

    private final LinkUtil linkUtil;
    private final ImageUtil imageUtil;
    private final ImageLoader imageLoader;
    private final ChatMessageService chatMessageService;

    @Override
    public String exportSessionNameTxt(Long userId) {
        ValidationUtil.validateUserId(userId);
        log.info("开始导出会话标题列表，userId: {}", userId);

        List<ChatSession> sessions = this.lambdaQuery()
                .select(ChatSession::getSessionName)
                .eq(ChatSession::getUserId, userId)
                .list();

        if (sessions.isEmpty()) {
            log.warn("导出会话标题列表失败：未找到用户会话，userId: {}", userId);
            throw new CustomerException(ResultEnum.SESSION_NOT_FOUND);
        }

        String sessionNames = sessions.stream()
                .map(ChatSession::getSessionName)
                .collect(Collectors.joining("\n"));

        FileWriteUtil.writeSessionNameTxt(sessionNames, sessions.size());
        log.info("会话标题TXT 导出成功，userId: {}, 导出会话数量: {}", userId, sessions.size());
        return sessionNames;
    }

    @Override
    public String exportSessionHtml(Long userId, String sessionId, String imageCategory) {
        String processedImageCategory = CategoryUtil.getProcessedCategory(imageCategory, DEFAULT_IMAGE_CATEGORY);
        ValidationUtil.validateUserId(userId);
        ValidationUtil.validateSessionId(sessionId);
        ValidationUtil.validateImageCategory(imageCategory);
        log.info("开始导出会话为HTML，sessionId: {}, imageCategory: {}", sessionId, imageCategory);

        HtmlExporter htmlExporter = new HtmlExporter(linkUtil, imageUtil, imageLoader,
                processedImageCategory, userId, sessionId);
        return htmlExporter.processExport(userId, sessionId, this, chatMessageService);
    }

    @Override
    public String exportSessionXml(Long userId, String sessionId, String postCategory, String imageCategory) {
        ValidationUtil.validateUserId(userId);
        ValidationUtil.validateSessionId(sessionId);
        ValidationUtil.validatePostCategory(postCategory);
        ValidationUtil.validateImageCategory(imageCategory);
        log.info("开始导出会话为XML，sessionId: {}, postCategory: {}, imageCategory: {}",
                sessionId, postCategory, imageCategory);

        String processedPostCategory = CategoryUtil.getProcessedCategory(postCategory, DEFAULT_POST_CATEGORY);
        String processedImageCategory = CategoryUtil.getProcessedCategory(imageCategory, DEFAULT_IMAGE_CATEGORY);

        XmlExporter xmlExporter = new XmlExporter(linkUtil, imageUtil, imageLoader,
                processedPostCategory, processedImageCategory, userId, sessionId);
        return xmlExporter.processExport(userId, sessionId, this, chatMessageService);
    }

    @Override
    public String exportSessionExcel(Long userId, String sessionId, String postCategory, String imageCategory) {
        ValidationUtil.validateUserId(userId);
        ValidationUtil.validateSessionId(sessionId);
        ValidationUtil.validatePostCategory(postCategory);
        ValidationUtil.validateImageCategory(imageCategory);
        log.info("开始导出会话为Excel，sessionId: {}, postCategory: {}, imageCategory: {}",
                sessionId, postCategory, imageCategory);

        String processedPostCategory = CategoryUtil.getProcessedCategory(postCategory, DEFAULT_POST_CATEGORY);
        String processedImageCategory = CategoryUtil.getProcessedCategory(imageCategory, DEFAULT_IMAGE_CATEGORY);

        ExcelExporter excelExporter = new ExcelExporter(linkUtil, imageUtil, imageLoader,
                processedPostCategory, processedImageCategory, userId, sessionId);
        return excelExporter.processExport(userId, sessionId, this, chatMessageService);
    }

    @Override
    public String exportSessionCsv(Long userId, String sessionId, String postCategory, String imageCategory) {
        ValidationUtil.validateUserId(userId);
        ValidationUtil.validateSessionId(sessionId);
        ValidationUtil.validatePostCategory(postCategory);
        ValidationUtil.validateImageCategory(imageCategory);
        log.info("开始导出会话为CSV，sessionId: {}, postCategory: {}, imageCategory: {}",
                sessionId, postCategory, imageCategory);

        String processedPostCategory = CategoryUtil.getProcessedCategory(postCategory, DEFAULT_POST_CATEGORY);
        String processedImageCategory = CategoryUtil.getProcessedCategory(imageCategory, DEFAULT_IMAGE_CATEGORY);

        CsvExporter csvExporter = new CsvExporter(linkUtil, imageUtil, imageLoader,
                processedPostCategory, processedImageCategory, userId, sessionId);
        return csvExporter.processExport(userId, sessionId, this, chatMessageService);
    }
}